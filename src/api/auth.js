import axios from 'axios'
import { API_BASE_URL, IS_BUILD_MODE } from './config'
import { ElMessage } from 'element-plus'
import router from '../router' // 直接导入router实例

// 使用配置文件中的API URL
const API_URL = API_BASE_URL

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
    'Accept-Language': 'zh-CN',
    // 'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.99 Mobile Safari/537.36'
  }
})

// 添加请求拦截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    const timestamp = new Date().getTime()
    config.headers.timestamp = timestamp.toString()
    if (token) {
      config.headers.accessToken = token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 添加响应拦截器
apiClient.interceptors.response.use(
  response => {
    if (response.data.code === 0 || response.data.success === true) {
      return response.data
    } else if (response.data.errCode == "2001") { // 未登录
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      // 使用router实例进行路由跳转
      router.push('/login')
      ElMessage.error(response.data.errMessage)
    } else if (response.data.errCode == "2010") { // 为激活
      // 使用router实例进行路由跳转
      router.push('/inviteActivate')
      ElMessage.success("请先激活邀请码")
    } else {
      console.log(response)
      return Promise.reject(new Error(response.data.msg || response.data.errMessage || '请求失败'))
    }
  },
  error => {
    return Promise.reject(error)
  }
)

// 获取手机验证码
export function getMobileVerificationCode (mobile) {
  return apiClient.post('/user/login/mobile/send-code', { mobile })
}

// 手机验证码登录
export function loginWithMobileCode (mobile, verifyCode) {
  return apiClient.post('/user/login/mobile', { mobile, verifyCode })
}

// 获取邮箱验证码
export function getEmailVerificationCode (email) {
  return apiClient.post('/user/login/send/verification/code', { email })
}

// 邮箱验证码登录
export function loginWithEmailCode (email, verifyCode) {
  return apiClient.post('/user/login/email', { email, verifyCode })
}

// 密码登录
export function loginWithPassword (email, password) {
  return apiClient.post('/user/login/password', { email, password })
}

// 获取微信扫码登录二维码URL
export function getWechatLoginQrCode () {
  return apiClient.get('/user/wechat/login/qrcode')
}

// 检查微信扫码登录状态
export function checkWechatLoginStatus (state) {
  return apiClient.get(`/user/wechat/login/check?state=${state}`)
}

// 发送注册验证码
export function sendRegisterVerificationCode (email) {
  return apiClient.post('/user/register/send/verification/code', { email })
}

// 用户注册
export function registerUser (email, password, verifyCode, inviteCode) {
  return apiClient.post('/user/register', {
    email,
    password,
    verifyCode,
    inviteCode
  })
}

/**
 * 获取当前用户的邀请码列表
 * @returns {Promise<Object>} - 包含邀请码信息的Promise对象
 */
export function getUserInvitationCodes() {
  return apiClient.get('/user/invitation-codes')
}

/**
 * 生成新的邀请码
 * @returns {Promise<Object>} - 包含新生成的邀请码信息的Promise对象
 */
export function generateInvitationCode() {
  return apiClient.post('/user/generate-invitation-code')
}

/**
 * 激活邀请码
 * @param {string} inviteCode - 要激活的邀请码
 * @returns {Promise<Object>} - 包含激活结果的Promise对象
 */
export function activateInvitationCode(inviteCode) {
  return apiClient.post(`/user/login/activate/${ inviteCode }`, { })
}

/**
 * 获取当前用户的积分账单历史记录
 * @param {Object} params - 分页查询参数
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @param {String} params.timeRange - 时间范围过滤，可选值: week, month, year
 * @returns {Promise<Object>} - 包含积分账单数据的Promise对象
 */
export function getUserPointsHistory(params = {}) {
  return apiClient.get('/user/points-history', {
    params: {
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
      timeRange: params.timeRange || '', 
      type: params.type || '', // 类型 1-注册奖励，2-邀请奖励，3-会话消耗，4-系统赠送，5-其他
      referenceId: params.referenceId || '' // 关联ID
    }
  })
}

/**
 * 获取当前用户的个人资料信息
 * @returns {Promise<Object>} - 包含用户个人资料的Promise对象
 */
export function getUserProfile() {
  return apiClient.get('/user/profile')
}

/**
 * 获取音色列表
 * @param {Object} params - 请求参数
 * @param {String} params.language - 语言筛选
 * @param {Number} params.sex - 性别筛选 (1为男, 2为女)
 * @param {Number} params.type - 类型筛选 (默认为1)
 * @returns {Promise} - 返回音色列表数据
 */
export function getVoiceList (params = {}) {
  return apiClient.get('/agent/v1/sound/list', {
    params: {
      language: params.language || '',
      sex: params.sex || '',
      type: params.type || ''
    }
  })
}

/**
 * 获取画面风格列表
 * @returns {Promise} - 返回画面风格列表数据
 */
export function getImageStyleList () {
  return apiClient.get('/agent/image/style/list')
}

/**
 * 获取创意标签列表
 * @returns {Promise} - 返回创意标签列表数据
 */
export function getTagList () {
  return apiClient.get('/agent/tag/list')
}

/**
 * 获取Ai创作的内容
 *
 * @param contentType 内容类型 1, 2, 3, 4, 5
 * @param conversationId 对话ID
 * @returns 返回获取到的Ai创作的内容
 */
export function getAiConversationsContentList (contentType, conversationId) {
  return apiClient.get('/agent/ai-creation/conversation/content', {
    params: {
      contentType: contentType || '',
      conversationId: conversationId || ''
    }
  })
}

/**
 * 创建一个新的对话
 * @param prompt 提示信息
 * @param soundId 声音文件的ID
 * @param imageSize 图像尺寸/比例
 * @param imageStyleId 图像风格ID
 * @param imageModel 图像模型ID
 * @param imageUrls 上传文件的objectName数组
 * @returns 返回创建的对话的响应数据
 */
export function createConversation (prompt, soundId, imageSize, imageStyleId, imageModel, imageUrls = []) {
  return apiClient.post('/agent/chat/conversation', {
    prompt,
    soundId,
    imageSize,
    imageStyleId,
    imageModel,
    imageUrls
  })
}

/**
 * 发布AI创作作品
 *
 * @param publish 发布状态，true为发布，false为不发布
 * @param visualRecordId 作品ID
 * @returns 返回发布结果
 */
export function aiCreationPublish (publish, visualRecordId) {
  return apiClient.post(`/agent/ai-creation/visual-record/status/update`, { publish, visualRecordId })
}

/**
 * 创建并渲染AI创作视觉记录
 *
 * @param {string} visualRecordId - 视觉记录的ID
 * @returns {Promise<any>} - 返回一个Promise，解析为API响应的结果
 */
export function aiCreationVisualRecord (visualRecordCode) {
  return apiClient.post(`/agent/ai-creation/visual-record/render`, { visualRecordCode })
}


/**
 * 获取会话列表
 * @param {number} pageNum - 当前页码
 * @param {number} pageSize - 每页条数
 * @param {string} status - 状态筛选 (0-进行中,1-已完成)
 * @returns 返回会话列表的 Promise 对象
 */
export function getConversationsList (pageNum, pageSize, status) {
  return apiClient.get('/agent/chat/sessions', {
    params: {
      pageNum,
      pageSize,
      status: status || ''
    }
  })
}

/**
 * 删除指定对话
 * @param {string} conversationId - 对话ID
 * @returns {Promise<void>} - 返回一个Promise，无返回值
 */
export function deleteConversation (conversationId) {
  return apiClient.delete(`/agent/chat/conversation/${conversationId}`, {})
}

export function deleteVisualRecord (visualRecordCode) {
  return apiClient.delete(`/agent/ai-creation/visual-record/${visualRecordCode}`, {})
}

/**
 * 获取会话历史生成的数据
 *
 * @param {string} conversationId - 会话ID
 * @returns {Promise<any>} - 返回一个Promise对象，解析后得到视觉列表数据
 */
export function getVisualList (conversationId) {
  return apiClient.get(`/agent/ai-creation/visual-record/list/${conversationId}`, {})
}



/**
 * 获取已发布的可视化记录列表（首页）
 *
 * @returns {Promise<any>} 返回包含已发布的可视化记录列表的Promise对象
 */
export function getVisualRecordListPublishedHome (pageNum, pageSize) {
  return apiClient.get(`/agent/ai-creation/visual-record/list/published/home`, {
    params: {
      pageNum,
      pageSize
    }
  })
}

/**
 * 获取视觉记录分享详情
 *
 * @param {string} shareToken - 分享token
 * @returns {Promise<any>} - 返回一个Promise对象，Promise对象解析后的值为视觉记录分享详情
 */
export function getVisualRecordShare (shareToken) {
  return apiClient.get(`/agent/ai-creation/visual-record/share/${shareToken}`, {})
}


/**
 * 获取已发布的视觉记录列表
 *
 * @param {string} visualRecordCode - 视觉记录代码
 * @returns {Promise<any>} 返回一个Promise对象，该对象解析为已发布的视觉记录列表
 */
export function getVisualRecordListPublished (visualRecordCode) {
  return apiClient.get(`/agent/ai-creation/visual-record/list/published/${visualRecordCode}`, {})
}

/**
 * 获取会话消息
 * @param conversationId 会话ID
 * @param limit 返回的消息数量，默认为20
 * @returns 返回Promise对象，resolve为会话消息数组，reject为错误信息
 */
export function getConversationMessages (conversation_id, limit = 10) {
  return apiClient.get('/agent/chat/user/messages', {
    params: {
      conversation_id: conversation_id || '',
      limit: limit || '',
    }
  })
}

/**
 * 上传图片到OSS
 * @param {File} file - 要上传的文件对象
 * @returns {Promise<Object>} - 包含上传结果的Promise对象，返回图片URL
 */
export function uploadImageToOSS(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return apiClient.post('/agent/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 更新用户个人资料
 * @param {Object} data - 包含用户资料的对象
 * @param {string} data.avatarUrl - 用户头像URL
 * @param {string} data.nickname - 用户昵称
 * @returns {Promise<Object>} - 返回包含更新结果的Promise对象
 */
export function updateUserProfile(data) {
  return apiClient.put('/user/profile', data)
}

/**
 * 绑定手机号
 * @param {string} mobile - 手机号
 * @param {string} code - 验证码
 * @returns {Promise<Object>} - 包含绑定结果的Promise对象
 */
export function bindMobile(mobile, code) {
  return apiClient.post('/user/mobile/bind', { mobile, code })
}

/**
 * 停止对话
 * @param {string} conversationId - 对话ID
 * @returns {Promise<Object>} - 包含停止结果的Promise对象
 */
export function stopConversation(conversationId) {
  return apiClient.post(`/agent/chat/stop-response/${conversationId}`)
}

/**
 * 清空上下文
 * @param {string} conversationId - 对话ID
 * @returns {Promise<Object>} - 包含清空结果的Promise对象
 */
export function clearContext(conversationId) {
  return apiClient.post(`/agent/chat/clear-context/${conversationId}`)
}

/**
 * 更新AI创作内容
 * @param {Object} contentData - 要更新的内容数据JSON
 * @param {Number} contentType - 内容类型(10-故事设计,1-故事,2-场景,4-分镜)
 * @param {String} conversationId - 会话ID
 * @returns {Promise} - 返回更新结果
 */
export function updateAiCreationContent(contentData, contentType, conversationId) {
  return apiClient.post('/agent/ai-creation/update/content', {
    contentData,
    contentType,
    conversationId
  });
}


/**
 * 查询章节列表
 * @param {string} conversationId - 对话ID
 * @returns {Promise<Object>} - 包含章节列表的Promise对象
 */
export function getShotChapters(conversationId) {
  return apiClient.get(`/agent/ai-shot/chapters/${conversationId}`)
}

/**
 * 查询分镜列表
 * @param {string} conversationId - 对话ID
 * @param {string} segmentId - 章节ID
 * @returns {Promise<Object>} - 包含分镜列表的Promise对象
 */
export function getShotShots(conversationId, segmentId) {
  return apiClient.get(`/agent/ai-shot/shots/${conversationId}/${segmentId}`)
}

/**
 * 修改分镜数据
 * @param {string} conversationId - 对话ID
 * @param {string} segmentId - 章节ID
 * @param {string} shotId - 分镜ID
 * @returns {Promise<Object>} - 包含修改结果的Promise对象
 */
export function updateShot(data) {
  return apiClient.post('/agent/ai-shot/update', data)
}

/**
 * 创建支付订单
 * @param {Object} orderData - 订单数据
 * @param {number} orderData.amount - 订单金额（单位：元）
 * @param {string} orderData.body - 商品描述
 * @param {number} orderData.payType - 支付方式：1-微信支付
 * @param {string} orderData.subject - 商品标题
 * @returns {Promise<Object>} - 包含订单信息的Promise对象
 */
export function createPayOrder(orderData) {
  return apiClient.post('/agent/pay/order/create', orderData)
}

/**
 * 查询支付订单状态
 * @param {string} orderNo - 订单编号
 * @returns {Promise<Object>} - 包含订单状态的Promise对象
 */
export function getPayOrderStatus(orderNo) {
  return apiClient.get(`/agent/pay/order/status?orderNo=${orderNo}`)
}

/**
 * 关闭支付订单
 * @param {string} orderNo - 订单编号
 * @returns {Promise<Object>} - 包含关闭结果的Promise对象
 */
export function closePayOrder(orderNo) {
  return apiClient.post(`/agent/pay/order/close?orderNo=${orderNo}`)
}

/**
 * 获取微信JS-SDK配置
 * @param {String} url - 当前页面URL，不包含#及其后面部分
 * @returns {Promise} 返回包含配置信息的Promise
 */
export function getWxJsConfig(url) {
  return apiClient.post('/agent/wechat/jssdk/signature', { url })
}

/**
 * 保存图片到历史记录
 * @param {String} contentId - 内容ID
 * @param {Number} contentType - 内容类型
 * @param {String} conversationId - 会话ID
 * @param {String} imageUrl - 图片URL
 */
export function uploadAiImageToOSS(contentId,contentType,conversationId,imageUrl,originalImageUrl) {
  return apiClient.post('/agent/ai-image/upload', {
    contentId,
    contentType,
    conversationId,
    imageUrl,
    originalImageUrl
  })
}

/**
 * 提交音色定制请求
 * 
 * @param {string} ossUrl - 上传到 OSS 的音频文件 URL
 * @param {string} name - 自定义音色名称
 * @param {number} sex - 音色性别 (1为男性, 2为女性)
 * @returns {Promise<Object>} - 返回音色定制请求的结果
 */
export function cloneVoice(ossUrl, name, sex) {
  return apiClient.post('/agent/minimax/file/voice-clone', {
    ossUrl,
    name,
    sex
  })
}

/**
 * 获取会话详情
 * @param {string} conversationId - 会话ID
 * @returns {Promise} - 返回会话详情数据
 */
export function getConversationDetail(conversationId) {
  return apiClient.get(`/agent/chat/session/${conversationId}`)
}

/**
 * 更新会话信息
 * @param {Object} data - 要更新的会话数据
 * @returns {Promise} - 返回更新结果
 */
export function updateConversation(data) {
  return apiClient.post('/agent/chat/session/update', data)
}

/**
 * 查询用户图片
 * @param {Object} params - 分页查询参数
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @param {Number} params.type - 图片类型: 1-全部 2-收藏角色 3-角色 4-分镜
 * @returns {Promise} - 返回用户图片数据
 */
export function getUserImage(params) {
  return apiClient.post(`/agent/user/image/query`, params)
}

/**
 * 查询用户视频
 * @param {Object} params - 分页查询参数
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @param {Number} params.videoType - 视频类型: 1-AI生成 2-渲染导出
 * @returns {Promise} - 返回用户视频数据
 */
export function getUserVideo(params) {
  return apiClient.post(`/agent/user/video/query`, params)
}

/**
 * 批量添加收藏
 * @param {Object} params - 收藏参数
 * @param {Array} params.resourceIds - 资源ID列表
 * @param {Number} params.resourceType - 资源类型(2-图片,3-视频)
 * @returns {Promise<Object>} - 包含收藏结果的Promise对象
 */
export function addFavorite(params) {
  return apiClient.post('/agent/resource/favorite/batch/add', params)
}

/**
 * 批量移除收藏
 * @param {Object} params - 收藏参数
 * @param {Array} params.resourceIds - 资源ID列表
 * @returns {Promise<Object>} - 包含收藏结果的Promise对象
 */
export function removeFavorite(params) {
  return apiClient.post('/agent/resource/favorite/batch/remove', params)
}

/**
 * 获取画布列表
 * @param {Object} params - 分页查询参数
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @returns {Promise<Object>} - 包含画布列表的Promise对象
 */
export function getCanvasList(params) {
  return apiClient.get('/agent/canvas/list', {
    params: {
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
    }
  })
}

/**
 * 更新画布
 * @param {Object} params - 画布参数
 * @param {Number} params.canvasId - 画布ID
 * @param {String} params.canvasName - 画布名称
 * @param {String} params.ratio - 画布比例
 * @returns {Promise<Object>} - 包含更新结果的Promise对象
 */
export function updateCanvas(params) {
  return apiClient.put(`/agent/canvas/update`, params)
}

/**
 * 创建画布
 */
export function createCanvas() {
  return apiClient.post('/agent/canvas/create')
}

/**
 * 删除画布
 * @param {string} canvasId - 画布ID
 * @returns {Promise<Object>} - 包含删除结果的Promise对象
 */
export function deleteCanvas(canvasId) {
  return apiClient.delete(`/agent/canvas/${canvasId}`)
}

/**
 * 获取会话章节ID的画布详情
 * @param {string} sessionId - 会话ID
 * @param {string} segmentId - 章节ID
 * @returns {Promise<Object>} - 包含章节列表的Promise对象
 */
export function getChapterCanvasDetail(sessionId, segmentId) {
  return apiClient.get(`/agent/canvas/session/${sessionId}/segment/${segmentId}`)
}

/**
 * 将章节转换为分镜
 * @param {string} sessionId - 会话ID
 * @param {string} segmentId - 章节ID
 * @returns {Promise<Object>} - 包含转换结果的Promise对象
 */
export function convertChapterToShot(sessionId, segmentId) {
  return apiClient.post(`/agent/canvas/convert-from-chapter`, {
    sessionId,
    segmentId
  })
}

/**
 * 获取画布详情
 * @param {string} canvasId - 画布ID
 * @returns {Promise<Object>} - 包含画布详情的Promise对象
 */
export function getCanvasDetail(canvasId) {
  return apiClient.get(`/agent/canvas/detail/${canvasId}`)
}

/**
 * 添加画布分镜
 * @param {Object} params - 画布分镜参数
 * @param {Number} params.canvasId - 画布ID
 * @param {Number} params.insertPosition - 插入位置
 * @returns {Promise<Object>} - 包含画布分镜的Promise对象
 */
export function createCanvasShot(params) {
  return apiClient.post(`/agent/canvas/shot/create`, params)
}

/**
 * 删除画布分镜
 * @param {string} shotId - 分镜ID
 * @returns {Promise<Object>} - 包含删除结果的Promise对象
 */
export function deleteCanvasShot(shotId) {
  return apiClient.delete(`/agent/canvas/shot/${shotId}`)
}

/**
 * 编辑画布分镜
 * @param {Object} params - 画布分镜对象
 * @returns {Promise<Object>} - 包含编辑结果的Promise对象
 */
export function editCanvasShot(params) {
  return apiClient.put(`/agent/canvas/shot/edit`, params)
}

/**
 * 更新画布分镜顺序
 * @param {Object} params - 画布分镜对象
 * @param {Number} params.canvasId - 画布ID
 * @param {Array} params.shotOrders - 分镜顺序列表
 * @param {Number} params.shotOrders.id - 分镜ID
 * @param {Number} params.shotOrders.sortOrder - 排序顺序
 * @returns {Promise<Object>} - 包含更新结果的Promise对象
 */
export function updateCanvasShotOrder(params) {
  return apiClient.put(`/agent/canvas/shot-order`, params)
}

/**
 * 创建画布分镜的音频
 * @param {Object} params - 画布分镜音频参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.text - 文本
 * @param {String} params.voiceId - 音色ID
 * @param {Number} params.speed - 语速
 * @param {Number} params.vol - 音量
 * @param {Number} params.pitch - 语调
 * @param {String} params.emotion - 情感
 * @returns {Promise<Object>} - 包含创建结果的Promise对象
 */
export function createCanvasShotAudio(params) {
  return apiClient.post(`/agent/canvas/shot/audio/create`, params)
}

/**
 * 更新画布分镜音频
 * @param {Object} params - 画布分镜音频参数
 * @param {Number} params.audioId - 音频ID
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.text - 文本
 * @param {String} params.voiceId - 音色ID
 * @param {Number} params.speed - 语速
 * @param {Number} params.vol - 音量
 * @param {Number} params.pitch - 语调
 * @param {String} params.emotion - 情感
 */
export function updateCanvasShotAudio(params) {
  return apiClient.put(`/agent/canvas/shot/audio/update`, params)
}

/**
 * 获取视频渲染分享链接
 * @param {Object} params - 获取视频渲染分享链接参数
 * @param {Number} params.taskId - 任务ID
 * @param {Boolean} params.share 分享状态：true-分享，false-取消分享
 * @returns {Promise<Object>} - 包含分享链接的Promise对象
 */
export function videoRenderShareLink(params) {
  return apiClient.post(`/agent/video-render/share`, params)
}

/**
 * 分页查询已分享的视频记录
 * @param {Object} params - 分页查询参数
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @returns {Promise<Object>} - 包含分享链接的Promise对象
 */
export function getVideoRenderSharedList(params) {
  return apiClient.get(`/agent/video-render/shared/list`, { params })
}

/**
 * 获取视频渲染分享视频
 * @param {String} shareCode - 分享码
 * @returns {Promise<Object>} - 包含分享链接的Promise对象
 */
export function getVideoRenderSharedVideo(shareCode) {
  return apiClient.get(`/agent/video-render/shared/${shareCode}`)
}

/**
 * 删除画布分镜的音频
 * @param {String} audioId - 音频ID
 * @returns {Promise<Object>} - 包含删除结果的Promise对象
 */
export function deleteCanvasShotAudio(audioId) {
  return apiClient.delete(`/agent/canvas/shot/audio/${audioId}`)
}

/**
 * 更新画布分镜音频顺序
 * @param {Object} params - 画布分镜音频对象
 * @param {Number} params.shotId - 分镜ID
 * @param {Array} params.audioOrders - 音频顺序列表
 * @param {Number} params.audioOrders.audioId - 音频ID
 * @param {Number} params.audioOrders.sortOrder - 排序顺序
 * @returns {Promise<Object>} - 包含更新结果的Promise对象
 */
export function updateCanvasShotAudioOrder(params) {
  return apiClient.put(`/agent/canvas/shot/audio/order`, params)
}


/**
 * 生成图片
 * @param {Object} params - 生成图片参数
 * @param {String} params.prompt - 提示词
 * @param {String} params.referenceImageUrl - 参考图片URL
 * @param {String} params.aspectRatio - 图片比例
 * @param {Number} params.strength - 强度默认0.8 生成强度范围(0.1-1.0)
 * @param {Number} params.shotId - 分镜ID
 * @returns {Promise<Object>} - 包含生成结果的Promise对象
 */
export function generateCanvasImage(params) {
  return apiClient.post(`/agent/canvas/material/generate-image`, params)
}

/**
 * 生成视频
 * @param {Object} params - 生成视频参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.model - 模型 1.文生视频或首帧生视频（doubao-seedance-1-0-pro-250528）2.收尾帧生视频（doubao-seedance-1-0-lite-i2v-250428）
 * @param {String} params.prompt - 提示词
 * @param {String} params.firstFrameImage - 第一帧图片URL
 * @param {String} params.lastFrameImage - 最后一帧图片URL
 * @param {String} params.resolution - 分辨率
 * @param {String} params.ratio - 图片比例
 * @param {Number} params.duration - 视频时长 example: 5
 * @param {Number} params.fps - 帧率 example: 30
 * @returns {Promise<Object>} - 包含生成结果的Promise对象
 */
export function generateCanvasVideo(params) {
  return apiClient.post(`/agent/canvas/material/generate-video`, params)
}

/**
 * 批量查询分镜状态  
 * @param {Object} params - 查询状态参数
 * @param {Array} params.shotIds - 分镜ID列表
 * @returns {Promise<Object>} - 包含生成结果的Promise对象
 */ 
export function shotStatusBatch(params) {
  return apiClient.post(`/agent/canvas/shot/status/batch`, params)
}

/**
 * 添加画布素材
 * @param {Object} params - 画布素材参数
 * @param {Number} params.canvasId - 画布ID
 * @param {Number} params.materialType - 素材类型(1-图片,2-视频)
 * @param {String} params.materialUrl - 素材URL
 * @param {String} params.materialName - 素材名称
 * @param {String} params.materialDesc - 素材描述
 * @param {Number} params.materialDuration - 素材时长
 * @returns {Promise<Object>} - 包含添加结果的Promise对象
 */
export function addCanvasMaterial(params) {
  return apiClient.post(`/agent/canvas/material/add`, params)
}

/**
 * 删除画布素材
 * @param {Object} params - 画布素材参数
 * @param {Number} params.materialId - 素材ID
 * @returns {Promise<Object>} - 包含删除结果的Promise对象
 */
export function deleteCanvasMaterial(params) {
  return apiClient.delete(`/agent/canvas/material/${params.materialId}`)
}

/**
 * 分页查询画布素材
 * @param {Object} params - 分页查询参数
 * @param {Number} params.canvasId - 画布ID
 * @param {Number} params.pageNum - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @param {Number} params.materialType - 素材类型(1-图片,2-视频)
 * @returns {Promise<Object>} - 包含画布素材列表的Promise对象
 */
export function getCanvasMaterialList(params) {
  return apiClient.post(`/agent/canvas/material/page`, params)
}

/**
 * 导出视频
 * @param {Object} params - 导出视频参数
 * @param {Number} params.canvasId - 画布ID
 * @param {String} params.resolution - 分辨率 1080p, 720p, 470p
 * @param {Number} params.showSubtitle - 是否显示字幕 1-是 0-否
 * @param {Number} params.fps - 帧率 24, 30, 60
 * @returns {Promise<Object>} - 包含导出结果的Promise对象
 */
export function exportVideo(params) {
  return apiClient.post(`/agent/video-render/export`, params)
}

/**
 * 获取导出视频列表
 * @param {String} canvasId - 画布ID
 * @returns {Promise<Object>} - 包含导出视频列表的Promise对象
 */
export function getExportVideoList(canvasId) {
  return apiClient.get(`/agent/video-render/list`, {
    params: {
      canvasId: canvasId || ''
    }
  })
}

/**
 * 更新音色名称
 * @param {Object} params - 更新音色名称参数
 * @param {Number} params.soundId - 音色ID
 * @param {String} params.name - 音色名称
 * @returns {Promise<Object>} - 包含更新结果的Promise对象
 */
export function updateSoundName(params) {
  return apiClient.post(`/agent/v1/sound/update-name`, params)
}

/**
 * 删除音色
 * @param {String} soundId - 音色ID
 * @returns {Promise<Object>} - 包含删除结果的Promise对象
 */
export function deleteSound(soundId) {
  return apiClient.delete(`/agent/v1/sound/delete/${soundId}`)
}

/**
 * 获取章节 prompt
 * @param {String} chapterId - 章节ID
 * @returns {Promise<Object>} - 包含章节 prompt 的Promise对象
 */
export function getSegmentPrompt(chapterId) {
  return apiClient.get(`/agent/ai-shot/design-content/${chapterId}`)
}

/**
 * 延长视频
 * @param {Object} params - 延长视频参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.lastFrameImageUrl - 最后一帧图片URL
 * @returns {Promise<Object>} - 包含延长结果的Promise对象
 */
export function extendVideo(params) {
  return apiClient.post(`/agent/canvas/shot/extend-video`, params)
}

/**
 * 设置背景音乐
 * @param {Object} params - 设置背景音乐参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.audioUrl - 音频URL
 * @returns {Promise<Object>} - 包含设置结果的Promise对象
 */
export function setBackgroundMusic(params) {
  return apiClient.post(`/agent/canvas/background-music/set`, params)
}

/**
 * 对口型处理
 * @param {Object} params - 对口型处理参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.imageUrl - 图片URL
 * @param {Array} params.audioIds - 音频ID数组
 * @returns {Promise<Object>} - 包含处理结果的Promise对象
 */
export function processLipSync(params) {
  return apiClient.post(`/agent/canvas/shot/lip-sync`, params)
}