<template>
  <div class="my-stories-component">
    <!-- 页面标题 -->
    <!-- <div class="page-header"> -->
      <!-- <h1 class="page-title">我的故事</h1> -->
      <!-- <p class="page-subtitle">管理和查看您创作的所有故事</p> -->
    <!-- </div> -->

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="stories-grid skeleton-grid">
        <div class="skeleton-card" v-for="i in columnCount * 3" :key="i">
          <div class="skeleton-image"></div>
          <div class="skeleton-content">
            <div class="skeleton-header">
              <div class="skeleton-title"></div>
            </div>
            <div class="skeleton-desc" style="width: 70%"></div>
          </div>
        </div>
      </div>
      <div class="loading-text">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>加载会话列表...</span>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="filteredStories.length === 0" class="empty-state final-empty-state">
      <el-icon class="empty-icon"><Picture /></el-icon>
      <div class="empty-text">您可以创建第一个故事</div>
      <el-button type="primary" @click="goToCreate" class="empty-create-button">
        创作
      </el-button>
    </div>

    <!-- 会话列表 - 瀑布流布局 -->
    <div v-else class="waterfall-container">
      <vue-grid-waterfall
        :data-list="filteredStories"
        :columns="waterfallColumns"
        @getMoreData="loadMoreConversations"
        :loading="loadingMore"
        :bottom="20"
        class="stories-waterfall">
        <template #slot-scope="{ slotProps }">
          <el-card
            class="story-card waterfall-card"
            shadow="hover"
            @click="viewConversation(slotProps.data.sessionId)">
            <!-- :style="{ minHeight: slotProps.data.height }" -->
            <div class="story-card-content">
              <!-- 操作按钮区域 -->
              <div class="story-actions">
                <el-tooltip content="删除" placement="top">
                  <el-button link @click.stop="confirmDelete(slotProps.data.sessionId)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </el-tooltip>
              </div>

              <!-- 图片区域 -->
              <div class="story-image-container" :style="{ aspectRatio: slotProps.data.aspectRatio }">
                <img v-if="slotProps.data.imageUrl"
                  :src="slotProps.data.imageUrl+'?x-oss-process=image/resize,w_280'"
                  alt="会话封面"
                  @error="handleImageError($event, slotProps.data)"
                  @load="handleImageLoaded(slotProps.data)"
                  class="story-image" />

                <div class="no-image-placeholder" v-if="!slotProps.data.imageUrl || slotProps.data.imageError">
                  <el-icon>
                    <PictureRounded />
                  </el-icon>
                </div>
              </div>

              <!-- 信息区域 -->
              <div class="story-info">
                <div class="story-header">
                  <h3 class="story-title">{{ slotProps.data.name }}</h3>
                </div>
                <div class="story-meta">
                  <span class="story-date">编辑于: {{ formatDate(slotProps.data.created_at) }}</span>
                </div>
              </div>

              <div class="story-status" :class="slotProps.data.chapterCount > 0 ? 'normal' : 'archived'">
                {{ getStatusText(slotProps.data.chapterCount) }}
              </div>
            </div>
          </el-card>
        </template>
      </vue-grid-waterfall>
    </div>



    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="" width="400px" destroy-on-close custom-class="delete-dialog"
      :append-to-body="true">
      <div class="delete-dialog-content">
        <div class="delete-icon-container">
          <div class="warning-circle">
            <el-icon class="delete-warning-icon">
              <WarningFilled />
            </el-icon>
          </div>
        </div>
        <div class="delete-warning-text">
          <h3 class="delete-warning-title">确定要删除这个会话吗？</h3>
          <p class="delete-warning-desc">此操作将<span class="highlight-text">永久删除</span>该会话，所有聊天记录将无法恢复！</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteDialogVisible = false" class="cancel-button" plain :disabled="deleteLoading">
            取消
          </el-button>
          <el-button type="danger" @click="deleteConversation" :loading="deleteLoading" class="confirm-delete-button">
            <span class="delete-button-content">
              <el-icon class="delete-icon" v-if="!deleteLoading">
                <Delete />
              </el-icon>
              确认删除
            </span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Delete, WarningFilled, Picture, Loading, PictureRounded } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getConversationsList, deleteConversation as deleteConversationApi } from '@/api/auth.js'

const router = useRouter()
const loading = ref(true)
const loadingMore = ref(false)
const stories = ref([]) // 存储会话列表
const searchQuery = ref('')
const sortBy = ref('newest')
const statusFilter = ref('all') // 默认选中"正常"状态
const hasMore = ref(false)
const deleteDialogVisible = ref(false)
const conversationToDelete = ref(null)
const deleteLoading = ref(false)
// 用于骨架屏显示的数量
const columnCount = ref(12)
// 瀑布流列数
const waterfallColumns = ref(3)

// 分页相关参数
const pageNum = ref(1)
const pageSize = ref(30) // 每页加载30条数据

// 处理图片加载错误
const handleImageError = (_, conversation) => {
  if (conversation) {
    conversation.imageError = true
    conversation.imageLoading = false
  }
}

// 处理图片加载完成
const handleImageLoaded = (conversation) => {
  if (conversation) {
    conversation.imageLoading = false
  }
}

// 加载会话列表
const loadConversations = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    // 转换状态过滤值为API需要的格式
    let apiStatus = ''
    if (statusFilter.value !== 'all') {
      apiStatus = statusFilter.value === 'normal' ? '1' : '0'
    }

    const response = await getConversationsList(pageNum.value, pageSize.value, apiStatus)

    if (response && response.success && response.data) {
      if (response.data.length > 0) {
        // 转换数据格式以适配现有组件
        const newStories = response.data.map((item, index) => {
          // 处理图片比例，如果没有ratio字段或为null，默认为4:3
          // 为了测试，我们随机生成一些比例
          const testRatios = ['4:3', '16:9', '9:16', '1:1', '3:4']
          const imageSize = item.imageSize || testRatios[index % testRatios.length]
          const [width, height] = imageSize.split(':').map(Number)
          const aspectRatio = width / height

          // 根据比例计算瀑布流中的高度（基础宽度为220px）
          const baseWidth = 220
          const imageHeight = baseWidth / aspectRatio
          // 添加额外的内容高度（标题、日期、状态标签、内边距等）
          // 标题行：最多36px（两行），日期行：约16px，内边距：约30px，状态标签：约20px，额外空间：10px
          const contentHeight = 112
          const calculatedHeight = imageHeight + contentHeight

          return {
            conversationId: item.conversationId,
            sessionId: item.sessionId,
            name: item.title || item.prompt,
            description: item.prompt || '暂无描述',
            created_at: item.updateTime,
            status: item.status === 1 ? 'normal' : 'archived',
            soundId: item.soundId,
            imageUrl: item.imageUrl,
            imageLoading: true, // 添加图片加载状态
            imageError: false, // 添加图片错误状态
            chapterCount: item.chapterCount,
            imageSize: imageSize, // 保存原始比例
            aspectRatio: aspectRatio, // 计算后的宽高比
            height: calculatedHeight + 'px' // 瀑布流需要的高度
          }
        })

        // 如果是加载更多，则追加数据，否则替换数据
        if (isLoadMore) {
          stories.value = [...stories.value, ...newStories]
        } else {
          stories.value = newStories
        }

        // 判断是否还有更多数据
        hasMore.value = response.data.length === pageSize.value
      } else if (!isLoadMore) {
        stories.value = []
        hasMore.value = false
      } else {
        hasMore.value = false
      }
    } else {
      if (!isLoadMore) {
        stories.value = []
      }
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载会话列表失败:', error)
    ElMessage.error('加载会话列表失败')
    if (!isLoadMore) {
      stories.value = []
    }
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多会话
const loadMoreConversations = async () => {
  if (loading.value || loadingMore.value || !hasMore.value) return
  
  pageNum.value++
  await loadConversations(true)
}

// 根据窗口宽度更新骨架屏数量和瀑布流列数
const updateSkeletonCount = () => {
  const width = window.innerWidth
  if (width < 520) {
    columnCount.value = 6
    waterfallColumns.value = 1
  } else if (width < 720) {
    columnCount.value = 8
    waterfallColumns.value = 2
  } else if (width < 980) {
    columnCount.value = 9
    waterfallColumns.value = 3
  } else if (width < 1280) {
    columnCount.value = 12
    waterfallColumns.value = 4
  } else if (width < 1600) {
    columnCount.value = 15
    waterfallColumns.value = 5
  }  else if (width < 2000) {
    columnCount.value = 18
    waterfallColumns.value = 6
  } else {
    columnCount.value = 21
    waterfallColumns.value = 7
  }
}

// 过滤和排序会话
const filteredStories = computed(() => {
  let result = [...stories.value]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(story =>
      story.name.toLowerCase().includes(query) ||
      (story.description && story.description.toLowerCase().includes(query))
    )
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'newest':
        return b.created_at - a.created_at
      case 'oldest':
        return a.created_at - b.created_at
      case 'title-asc':
        return a.name.localeCompare(b.name)
      case 'title-desc':
        return b.name.localeCompare(a.name)
      default:
        return 0
    }
  })

  return result
})

// 监听状态过滤器变化
watch(() => statusFilter.value, () => {
  // 状态变化时，重置分页并重新加载数据
  pageNum.value = 1
  loadConversations()
})

// 状态文本映射
const getStatusText = (status) => {
  if (status > 0) {
    return `共 ${status} 章`
  } else {
    return '未完成'
  }
}

// 格式化日期，如果是今年则不显示年，显示时分秒
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  if (date.getFullYear() === new Date().getFullYear()) {
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } else {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
};

// 导航到创建页面
const goToCreate = () => {
  router.push('/inputSection')
}

// 查看会话详情
const viewConversation = (sessionId) => {
  router.push({
    path: '/create',
    query: {
      conversationId: sessionId
    }
  })
}

// 确认删除对话框
const confirmDelete = (conversationId) => {
  conversationToDelete.value = conversationId
  deleteDialogVisible.value = true
}

// 删除会话
const deleteConversation = async () => {
  if (deleteLoading.value) return

  try {
    deleteLoading.value = true

    // 调用API删除会话
    const response = await deleteConversationApi(conversationToDelete.value)

    if (response && response.success) {

      // 从本地数据中删除
      const index = stories.value.findIndex(s => s.sessionId === conversationToDelete.value)
      if (index !== -1) {
        stories.value.splice(index, 1)
      }

      ElMessage.success('已删除')
    }
  } catch (error) {
    console.error('删除会话失败:', error)
    ElMessage.error(error.response?.data?.message || '删除会话失败')
  } finally {
    deleteDialogVisible.value = false
    conversationToDelete.value = null
    deleteLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadConversations()
  updateSkeletonCount()
  window.addEventListener('resize', updateSkeletonCount)
})
</script>

<style scoped>
.my-stories-component {
  width: 100%;
  /* padding: 20px; */
}

/* 页面标题样式 */
.page-header {
  text-align: center;
  padding: 10px 0;
  text-align: left;
}

.page-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6365f1b0, #8a5cf6b7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: title-glow 3s ease-in-out infinite alternate;
}

.page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

body.dark .page-title {
  color: #e5e7eb;
}

body.dark .page-subtitle {
  color: #9ca3af;
}

@keyframes title-glow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.1);
  }
}

.loading-state {
  margin: 40px 0;
  min-height: 400px;
}

.loading-text {
  text-align: center;
  color: #64748b;
  margin-top: 30px;
  font-size: 14px;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text .loading-icon {
  animation: rotate 1.5s linear infinite;
  color: #6366f1;
  font-size: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

/* 特殊空状态样式 */
.final-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  height: calc(100vh - 160px);
  justify-content: center;
  text-align: center;
  border-radius: 12px;
  z-index: 1;
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0);
    scale: 1;
  }
  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

.empty-create-button {
  margin-top: 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  padding: 10px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.empty-create-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

.waterfall-container {
  width: 100%;
  margin-bottom: 20px;
}

.stories-waterfall {
  width: 100%;
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 4px;
  margin-bottom: 20px;
  width: 100%;
}

.stories-grid-container {
  display: contents;
}

/* 卡片进场动画 */
.story-item-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.story-item-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  /* 移除 position: absolute 避免全屏闪烁 */
  z-index: 1;
}

.story-item-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.story-item-leave-to {
  opacity: 0;
  transform: scale(0.8);
  filter: blur(2px);
}

/* 删除时的移动动画 */
.story-item-move {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 为Grid布局中的卡片添加不同的动画延迟 */
.stories-grid > .story-card:nth-child(3n+1) {
  transition-delay: 0.05s;
}
.stories-grid > .story-card:nth-child(3n+2) {
  transition-delay: 0.1s;
}
.stories-grid > .story-card:nth-child(3n+3) {
  transition-delay: 0.15s;
}

.story-card {
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s, box-shadow 0.3s;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  --el-card-padding: 0px;
  overflow: hidden;
  background-color: --white;
  border-radius: 8px;
  box-shadow: 0 4px 14px #7b7dfc52;
  animation: card-appear 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.waterfall-card {
  margin-bottom: 4px;
  opacity: 1;
  transform: translateY(0);
  animation: none;
  height: auto !important; /* 允许高度自适应内容 */
  min-height: auto; /* 移除最小高度限制 */
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.story-card:hover {
  transform: scale(1.03);
  z-index: 2;
  box-shadow: 0 8px 20px rgba(123, 125, 252, 0.4);
}

.story-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative; /* 添加相对定位，作为绝对定位的参考 */
}

.story-image-container {
  position: relative;
  overflow: hidden;
  /* background-color: #f1f5f9; */
  transition: background-color 0.3s;
  margin: 0;
  border-radius: 8px;
  margin: 10px 10px 0 10px;
}

/* 为非瀑布流布局保持原有的固定比例 */
.stories-grid .story-image-container {
  aspect-imageSize: 4/3;
}

.story-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: 0.8s all;
}

.story-image:hover {
  object-position: left top;
}

@keyframes image-appear {
  from {
    opacity: 0;
    filter: blur(8px);
    transform: scale(1.05);
  }
  to {
    opacity: 1;
    filter: blur(0);
    transform: scale(1);
  }
}

.story-info {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  gap: 4px;
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.story-title {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 允许显示两行标题 */
  line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: left;
  flex: 1;
  min-width: 0;
  transition: color 0.3s;
  /* max-height: 36px; */
}

.story-status {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 2px 4px;
  border-radius: 8px 0 8px 0;
  font-size: 10px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
  flex-shrink: 0;
}

.story-status.normal {
  background-color: #10b981;
}

.story-status.archived {
  background-color: #f59e0b;
}

.story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.story-date {
  font-size: 11px;
  color: #9eafc6;
  text-align: left;
  transition: color 0.3s;
  line-height: 1.3;
  margin-top: 2px;
}

.story-actions {
  display: flex;
  gap: 0px;
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.story-card:hover .story-actions {
  opacity: 1;
}

body.dark .story-actions {
  background-color: rgba(30, 41, 59, 0.8);
}

.story-actions .el-button {
  color: #64748b;
  padding: 4px 8px;
  transition: color 0.3s, background-color 0.3s;
}

.story-actions .el-button:hover {
  color: #3b82f6;
  background-color: #f1f5f9;
}

body.dark .story-actions .el-button:hover {
  background-color: var(--bg-hover);
}

.story-actions .el-icon {
  font-size: 16px;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 20px;
}

.load-more-button {
  width: 200px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.load-more-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.button-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.button-pulse {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.2);
  animation: button-pulse 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  opacity: 0;
}

@keyframes button-pulse {
  0% {
    transform: scale(0.1);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 图片占位符样式 */
.no-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b8d;
  font-size: 16px;
  gap: 12px;
}

body.dark .no-image-placeholder {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
}

.no-image-placeholder .el-icon {
  font-size: 36px;
  opacity: 0.2;
}

/* 删除对话框样式 */
.delete-dialog-content {
  display: flex;
  align-items: flex-start;
  padding: 20px 0;
}

.delete-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  margin-right: 20px;
  flex-shrink: 0;
  position: relative;
}

.warning-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: rgba(245, 158, 11, 0.15);
  border-radius: 50%;
  animation: pulse 2s infinite;
  position: relative;
}

.warning-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(245, 158, 11, 0.3);
  animation: ripple 1.5s ease-out infinite;
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.delete-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.delete-warning-icon {
  font-size: 32px;
  color: #f59e0b;
}

.delete-warning-text {
  flex: 1;
}

.delete-warning-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.delete-warning-desc {
  color: #64748b;
  font-size: 15px;
  line-height: 1.6;
}

.highlight-text {
  color: #ef4444;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.cancel-button {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.confirm-delete-button {
  background: linear-gradient(to right, #f59e0b, #ef4444);
  border: none;
  font-weight: 500;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

.confirm-delete-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px -2px rgba(239, 68, 68, 0.3);
}

.delete-icon {
  font-size: 16px;
  margin-right: 4px;
}

/* 自定义对话框样式 */
:deep(.delete-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease;
}

:deep(.delete-dialog .el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  margin-right: 0;
}

:deep(.delete-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

:deep(.delete-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.delete-dialog .el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 0;
}

:deep(.delete-dialog .el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

:deep(.delete-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #94a3b8;
  font-size: 18px;
  transition: all 0.3s ease;
}

:deep(.delete-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: #f59e0b;
  transform: rotate(90deg);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* 骨架屏样式 */
.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  width: 100%;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  animation: skeleton-pulse 1.5s infinite ease-in-out;
}

body.dark .skeleton-card {
  background: rgba(30, 41, 59, 0.8);
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-image {
  width: 100%;
  height: 130px;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

body.dark .skeleton-image {
  background: linear-gradient(110deg, #1e293b 30%, #273449 50%, #1e293b 70%);
  background-size: 200% 100%;
}

.skeleton-content {
  padding: 15px;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-title {
  height: 24px;
  width: 70%;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

.skeleton-desc {
  height: 16px;
  width: 100%;
  margin-bottom: 8px;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

body.dark .skeleton-title,
body.dark .skeleton-desc {
  background: linear-gradient(110deg, #1e293b 30%, #273449 50%, #1e293b 70%);
  background-size: 200% 100%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .my-stories-component {
    padding: 10px;
  }

  .story-card:hover {
    transform: scale(1.02);
  }
}
</style>
