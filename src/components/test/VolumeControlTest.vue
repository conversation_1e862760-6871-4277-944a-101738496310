<template>
  <div class="volume-control-test">
    <h2>音量控制组件测试</h2>
    
    <div class="test-container">
      <VolumeControlPanel
        :current-shot="testCurrentShot"
        :background-music="testBackgroundMusic"
        @update:backgroundMusicVolume="onBackgroundMusicVolumeChange"
        @update:videoVolume="onVideoVolumeChange"
        @update:audioVolume="onAudioVolumeChange"
      />
    </div>
    
    <div class="test-info">
      <h3>测试数据</h3>
      <div class="info-section">
        <h4>背景音乐:</h4>
        <pre>{{ JSON.stringify(testBackgroundMusic, null, 2) }}</pre>
      </div>
      
      <div class="info-section">
        <h4>当前分镜:</h4>
        <pre>{{ JSON.stringify(testCurrentShot, null, 2) }}</pre>
      </div>
      
      <div class="info-section">
        <h4>事件日志:</h4>
        <div class="event-log">
          <div v-for="(event, index) in eventLog" :key="index" class="event-item">
            {{ event }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import VolumeControlPanel from '@/components/videoEdit/VolumeControlPanel.vue';

// 测试数据
const testBackgroundMusic = ref({
  id: 'bg-music-1',
  name: '背景音乐测试',
  audioUrl: 'https://example.com/bg-music.mp3',
  volume: 0.8,
  audioDuration: 120000
});

const testCurrentShot = ref({
  id: 'shot-1',
  type: 'video',
  videoUrl: 'https://example.com/video.mp4',
  videoVolume: 0.6,
  audios: [
    {
      id: 'audio-1',
      text: '这是第一段音频',
      audioType: 1,
      volume: 0.7,
      audioDuration: 5000
    },
    {
      id: 'audio-2',
      audioType: 2,
      volume: 0.9,
      audioDuration: 3000
    },
    {
      id: 'audio-3',
      text: '这是第三段比较长的音频文本内容',
      audioType: 1,
      volume: 0.5,
      audioDuration: 8000
    }
  ]
});

// 事件日志
const eventLog = ref([]);

// 事件处理方法
const onBackgroundMusicVolumeChange = (volume) => {
  const message = `背景音乐音量变化: ${volume}`;
  console.log(message);
  eventLog.value.unshift(message);
  
  // 更新测试数据
  testBackgroundMusic.value.volume = volume;
};

const onVideoVolumeChange = (volume) => {
  const message = `视频音量变化: ${volume}`;
  console.log(message);
  eventLog.value.unshift(message);
  
  // 更新测试数据
  testCurrentShot.value.videoVolume = volume;
};

const onAudioVolumeChange = (volume) => {
  const message = `整体音频音量变化: ${volume}`;
  console.log(message);
  eventLog.value.unshift(message);

  // 更新测试数据 - 为所有音频设置相同音量
  testCurrentShot.value.audios.forEach(audio => {
    audio.volume = volume;
  });
};
</script>

<style scoped>
.volume-control-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  width: 320px;
  margin: 20px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.test-info {
  margin-top: 30px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.info-section pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.event-log {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background: #f9f9f9;
}

.event-item {
  padding: 2px 0;
  font-size: 12px;
  color: #606266;
  border-bottom: 1px solid #eee;
}

.event-item:last-child {
  border-bottom: none;
}

body.dark .volume-control-test {
  color: var(--text-primary-dark);
}

body.dark .info-section h4 {
  color: var(--text-primary-dark);
}

body.dark .info-section pre {
  background: var(--bg-tertiary-video);
  color: var(--text-primary-dark);
}

body.dark .event-log {
  background: var(--bg-tertiary-video);
  border-color: var(--border-color-dark);
}

body.dark .event-item {
  color: var(--text-secondary-dark);
  border-bottom-color: var(--border-color-dark);
}
</style>
