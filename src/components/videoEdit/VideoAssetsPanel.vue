<template>
  <div class="video-assets-panel">
    <!-- 素材类型选择 -->
    <div class="asset-type-selector">
      <!-- <el-tabs v-model="activeAssetType" class="asset-category-tabs asset-selector-tabs" @tab-click="handleTabChange">
        <el-tab-pane label="项目素材" name="project"></el-tab-pane>
      </el-tabs> -->

      <!-- 添加素材类型选择 -->
      <el-tabs v-model="activeMaterialType" class="asset-category-tabs asset-selector-tabs material-type-tabs"
        @tab-click="handleMaterialTypeChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="图片" name="image"></el-tab-pane>
        <el-tab-pane label="视频" name="video"></el-tab-pane>
        <el-tab-pane label="音频" name="audio"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 上传按钮区域 -->
    <div class="upload-area">
      <div class="upload-button" @click="openReferenceSelector">
        <el-icon>
          <Plus />
        </el-icon>
        <span>上传项目素材</span>
      </div>
    </div>

    <!-- 素材列表 -->
    <div class="assets-list-container">
      <!-- 素材列表 -->
      <div class="assets-list-wrapper">
        <div class="assets-list">
          <div v-for="(asset, index) in materialList" :key="asset.id || index" class="asset-item"
            @click="selectAsset(asset)">
            <!-- 根据素材类型显示不同内容 -->
            <div class="asset-thumbnail"
              :class="{ 'video-thumbnail': asset.materialType == 2, 'audio-thumbnail': asset.materialType == 3, 'image-thumbnail': asset.materialType == 1 }">
              <!-- 删除按钮 -->
              <div class="delete-button" @click.stop="deleteMaterial(asset.id, $event)">
                <el-icon>
                  <Delete />
                </el-icon>
              </div>

              <div class="play-button" @click.stop="playMaterial(asset)">
                <el-icon>
                  <View />
                </el-icon>
              </div>

              <!-- 音频类型素材 -->
              <div v-if="asset.materialType === 3" class="audio-item">
                <!-- <div class="audio-icon">
                  <el-icon><Headset /></el-icon>
                </div> -->
                <div class="audio-waveform">
                  <div class="wave-bar" v-for="i in 9" :key="i"></div>
                </div>
                <div class="audio-duration" v-if="asset.materialDuration">
                  {{ formatDuration(asset.materialDuration) }}
                </div>
              </div>

              <!-- 图片类型素材 -->
              <img v-else-if="asset.materialType === 1" :src="`${asset.materialUrl}?x-oss-process=image/resize,w_200`"
                :alt="asset.materialName" />
              <!-- "`${element.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" -->

              <!-- 视频类型素材 -->
              <template v-else-if="asset.materialType == 2">
                <img v-if="asset.materialUrl" :src="`${asset.materialUrl}?x-oss-process=video/snapshot,t_0,f_jpg`"
                  :alt="asset.materialName" />
                <!-- 如果都没有，显示占位符 -->
                <div v-else class="video-placeholder">
                  <el-icon>
                    <VideoCamera />
                  </el-icon>
                </div>
                <div class="video-duration" v-if="asset.videoMaterialParams?.duration">
                  {{ formatDuration(asset.videoMaterialParams.duration) }}
                </div>
              </template>
            </div>
            <div class="asset-info">
              <div class="asset-name">{{ getAssetName(asset) }}</div>
            </div>
          </div>
        </div>

        <!-- 加载中状态 -->
        <div v-if="isLoading || isProjectLoading" class="loading-state">
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
          <div class="loading-text">加载素材中...</div>
        </div>

        <!-- 空状态 -->
        <div v-if="!isLoading && !isProjectLoading && materialList.length === 0" class="empty-state">
          <el-icon>
            <DocumentDelete />
          </el-icon>
          <div class="empty-text">暂无素材</div>
        </div>
      </div>

      <!-- 项目素材分页控件 -->
      <div class="asset-pagination" v-if="totalProjectAssets > 0">
        <el-pagination v-model:current-page="projectPageNum" :page-size="projectPageSize" :pager-count="5"
          layout="prev, pager, next" :total="totalProjectAssets" @current-change="handleProjectPageChange" />
      </div>
    </div>
  </div>

  <!-- 引用选择器弹框 -->
  <ReferenceSelector v-model:visible="showReferenceSelector" :canvasId="canvasId" title="选择项目素材"
    @select="handleAssetSelected" @upload-success="handleUploadSuccess" />
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { Search, Upload, Loading, DocumentDelete, Plus, View, VideoCamera, Delete, Headset } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserImage, addCanvasMaterial, getCanvasMaterialList, deleteCanvasMaterial } from '@/api/auth.js';
import { uploadToOSS } from '@/api/oss.js';
import ReferenceSelector from './ReferenceSelector.vue';

// 组件属性
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  activeTabFromParent: {
    type: String,
    default: ''
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

// 事件
const emit = defineEmits(['select-asset', 'upload', 'tab-change']);

// 状态变量
const activeAssetType = ref('project');
const searchQuery = ref('');
// 添加素材类型选择状态
const activeMaterialType = ref('all');
// API返回的原始素材数据
const materialList = ref([]);

// 文件上传相关状态
const fileInputRef = ref(null);
const selectedFile = ref(null);
const isUploading = ref(false);
const uploadProgress = reactive({});

// 多文件上传相关状态
const multipleFiles = ref([]);
const uploadingFiles = ref([]);

// 引用选择器状态
const showReferenceSelector = ref(false);

// 项目素材分页状态
const projectPageNum = ref(1);
const projectPageSize = ref(24);
const totalProjectAssets = ref(0);
const isProjectLoading = ref(false);

// 监听父组件传入的激活标签页
watch(() => props.activeTabFromParent, (newValue) => {
  if (newValue && newValue === 'project') {
    activeAssetType.value = newValue;
  }
}, { immediate: true });

// 获取素材名称
const getAssetName = (asset) => {
  // 使用素材名称或描述
  if (asset.materialName) {
    return asset.materialName.length > 20 ? asset.materialName.substring(0, 20) + '...' : asset.materialName;
  }
  if (asset.materialDesc) {
    return asset.materialDesc.length > 20 ? asset.materialDesc.substring(0, 20) + '...' : asset.materialDesc;
  }
  return asset.id ? `素材${asset.id}` : '未命名素材';
};

// 选择素材
const selectAsset = (asset) => {
  if (asset.materialType == 3) {
    ElMessageBox.confirm('将添加音频到背景音', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      emit('select-asset', {
        type: asset.materialType == 1 ? 'image' : asset.materialType == 3 ? 'audio' : 'video',
        asset
      });
    });
  } else {
    ElMessageBox.confirm('选择该素材后，将替换当前分镜的素材', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      emit('select-asset', {
        type: asset.materialType == 1 ? 'image' : asset.materialType == 3 ? 'audio' : 'video',
        asset
      });
    });
  }
};


// 播放素材
const playMaterial = (asset) => {
  console.log('播放素材:', asset);
  if (asset.materialType == 1) { // 图片
    window.openImagePreview(asset.materialUrl, {
      title: asset.materialName,
      description: asset.imageMaterialParams?.prompt || ''
    });
  } else if (asset.materialType == 2) { // 视频
    window.openVideoPreview(asset.materialUrl, asset.videoMaterialParams?.prompt || '', {
      title: getAssetName(asset),
      description: asset.videoMaterialParams?.duration || ''
    });
  } else if (asset.materialType == 3) { // 音频
    // 播放音频
    window.openAudioPreview(asset.materialUrl, {
      title: getAssetName(asset),
    });
  }
};


// 加载项目素材数据
const fetchProjectMaterials = async () => {
  // 如果没有canvasId，则不加载项目素材
  if (!props.canvasId) {
    materialList.value = [];
    return;
  }

  isProjectLoading.value = true;

  try {
    const params = {
      canvasId: Number(props.canvasId),
      pageNum: projectPageNum.value,
      pageSize: projectPageSize.value
    };

    // 添加素材类型筛选条件
    console.log('fetchProjectMaterials - activeMaterialType:', activeMaterialType.value);
    if (activeMaterialType.value !== 'all') {
      params.materialType = activeMaterialType.value == 'image' ? 1 : activeMaterialType.value == 'audio' ? 3 : 2; // 1-图片,2-视频,3-音频
      console.log('fetchProjectMaterials - materialType filter:', params.materialType);
    }

    const response = await getCanvasMaterialList(params);

    if (response.success) {
      // 直接使用API返回的数据
      materialList.value = response.data || [];
      totalProjectAssets.value = response.totalCount || 0;
    } else {
      ElMessage.error(response.errMessage || '获取项目素材失败');
      materialList.value = [];
    }
  } catch (error) {
    console.error('获取项目素材失败:', error);
    ElMessage.error('获取项目素材失败，请重试');
    materialList.value = [];
  } finally {
    isProjectLoading.value = false;
  }
};

// 打开引用选择器
const openReferenceSelector = () => {
  showReferenceSelector.value = true;
};

// 处理资产选择
const handleAssetSelected = (asset) => {
  console.log('选择了资产:', asset);
  // 可以在这里添加额外的处理逻辑
  fetchProjectMaterials();
};

// 处理上传成功
const handleUploadSuccess = () => {
  // 重新加载项目素材列表
  fetchProjectMaterials();
};

// 处理标签页切换
const handleTabChange = (tab) => {
  console.log('Tab changed to:', tab);
  emit('tab-change', tab.name);
};

// 处理素材类型切换
const handleMaterialTypeChange = (tab) => {
  console.log('Material type changed to:', tab.props.name);
  console.log('Previous activeMaterialType:', activeMaterialType.value);
  // 确保 activeMaterialType 立即更新
  activeMaterialType.value = tab.props.name;
  console.log('Updated activeMaterialType:', activeMaterialType.value);
  // 重新加载素材列表
  fetchProjectMaterials();
};

// 格式化时长，将毫秒转换为 mm:ss 格式
const formatDuration = (ms) => {
  if (!ms) return '00:00';

  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};

// 处理视频加载完成
const onVideoLoaded = (event) => {
  const video = event.target;
  const assetId = video.dataset.assetId;

  // 设置视频时间为第一帧
  video.currentTime = 0.1;

  // 可以在这里添加逻辑来捕获首帧并保存为图片
  console.log('视频加载完成，资产ID:', assetId);
};

// 处理视频加载错误
const onVideoError = (event) => {
  const video = event.target;
  const assetId = video.dataset.assetId;
  console.error('视频加载失败，资产ID:', assetId);
};

// 加载组件数据
onMounted(() => {
  // 加载项目素材
  if (props.canvasId) {
    fetchProjectMaterials();
  } else {
    console.warn('未提供canvasId，无法加载项目素材');
  }



  // 监听canvasId变化，重新加载项目素材
  watch(() => props.canvasId, (newVal) => {
    if (newVal) {
      fetchProjectMaterials();
    } else {
      materialList.value = [];
    }
  });
});

// 刷新素材列表的方法 - 提供给父组件调用
const refreshMaterials = () => {
  fetchProjectMaterials();
};

// 暴露方法给父组件
defineExpose({
  fetchProjectMaterials,
  refreshMaterials
});



// 处理项目素材页面变更
const handleProjectPageChange = (page) => {
  projectPageNum.value = page;
  fetchProjectMaterials();
};



// 删除素材
const deleteMaterial = async (materialId, event) => {
  // 阻止事件冒泡，避免触发选择素材
  if (event) {
    event.stopPropagation();
  }

  try {
    // 显示确认对话框
    await ElMessageBox.confirm('确定要删除该素材吗？删除后无法恢复。', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 调用删除API
    const result = await deleteCanvasMaterial({ materialId });

    if (result.success) {
      ElMessage.success('素材删除成功');
      // 刷新素材列表
      fetchProjectMaterials();
    } else {
      ElMessage.error(result.errMessage || '删除素材失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除素材失败:', error);
      ElMessage.error('删除素材失败，请重试');
    }
  }
};

</script>

<style scoped>
.video-assets-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.asset-type-selector {
  /* margin-bottom: 15px; */
  /* padding: 10px 10px 0 10px; */
  border-bottom: 1px solid #8585852a;
}

/* 素材类型选择标签页样式 */
.material-type-tabs {
  margin-top: 0;
  padding: 0 16px;
}

.material-type-tabs :deep(.el-tabs__header) {
  margin-bottom: 10px !important;
}

.material-type-tabs :deep(.el-tabs__item) {
  padding: 0 12px !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 13px !important;
}

/* 上传区域样式 */
.upload-area {
  margin: 10px 16px 0 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.upload-button {
  width: 290px;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 84px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  width: fit-content;
}

body.dark .upload-area {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .upload-area:hover {
  background-color: rgba(204, 221, 255, .1);
}

.upload-progress {
  margin-top: 10px;
}

.upload-progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: center;
}

body.dark .upload-progress-text {
  color: var(--text-secondary);
}

/* 参考图选择器样式 */
.reference-selector-container {
  /* padding: 10px; */
}

.reference-selector-tabs {
  display: flex;
  /* gap: 6px; */
  /* margin-bottom: 10px; */
}

.reference-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
}

.reference-tab:hover {
  background-color: #ecf5ff;
}

.reference-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .reference-tab {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .reference-tab:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .reference-tab.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
  color: var(--primary-color);
}

/* 本地上传面板样式 */
.local-upload-panel {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 10px;
}

.upload-area-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 1px dashed #c0c4cc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area-inner:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #909399;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

body.dark .local-upload-panel {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .upload-area-inner {
  border-color: #606266;
}

body.dark .upload-area-inner:hover {
  border-color: var(--primary-color);
}

body.dark .upload-icon,
body.dark .upload-hint {
  color: var(--text-secondary);
}

body.dark .upload-text {
  color: var(--text-primary);
}

/* 资产选择面板样式 */
.asset-selector-panel {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 10px;
}

.asset-selector-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 14px;
  color: #909399;
}

.empty-assets {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  /* margin-bottom: 10px; */
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.asset-preview {
  aspect-ratio: 4/3;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

.asset-image.loaded {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

.asset-pagination {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  flex-shrink: 0;
  /* 防止分页控件被压缩 */
}

body.dark .asset-selector-panel {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

body.dark .loading-text,
body.dark .empty-text {
  color: var(--text-secondary);
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

/* 资产分类标签页样式 */
.asset-category-tabs {
  /* margin-bottom: 10px; */
  margin: 6px 0 0 0;
}

/* 添加更具体的选择器以覆盖父组件样式 */
.asset-selector-tabs :deep(.el-tabs__header) {
  /* margin-top: 10px !important; */
  position: relative !important;
  margin: 0 !important;
}

.asset-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 38px !important;
  line-height: 38px !important;
  transition: all 0.3s !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #4f46e5 0, #4f46e5 100%,
      transparent 0, transparent) !important;
  height: 2px !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #6366f1 0, #6366f1 100%,
      transparent 0, transparent) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

.search-box {
  margin-bottom: 15px;
}

.assets-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 12px 0 12px;
  overflow: hidden;
  position: relative;
  /* 添加相对定位，作为绝对定位元素的参考 */
}

.assets-list-wrapper {
  flex: 1;
  overflow-y: auto;
  padding-right: 6px;
  min-height: 0;
}

.assets-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  min-height: 0;
}

.asset-item {
  /* border: 1px solid #ebeef5; */
  /* border-radius: 4px; */
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  /* background-color: #fff; */
}

body.dark .asset-item {
  /* border-color: var(--border-color);
  background-color: var(--bg-secondary); */
}

.asset-item:hover .asset-thumbnail {
  /* transform: translateY(-2px); */
  box-shadow: 0 2px 12px 0 rgba(144, 138, 226, 0.1);
}

.asset-item:hover .asset-thumbnail img {
  /* object-fit: contain; */
  object-position: left top;
}

body.dark .asset-item:hover .asset-thumbnail {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.asset-thumbnail {
  /* height: 94px; */
  /* overflow: hidden; */
  aspect-ratio: 4/3;
  position: relative;
  border-radius: 4px;
  /* box-sizing: border-box; */
}

.asset-thumbnail img {
  border-radius: 4px;
  width: 100%;
  height: 100%;
  object-fit: cover;
  aspect-ratio: 4/3;
  /* aspect-ratio: 16/9; */
  background-color: #f5f7fa;
  object-position: center center;
  transition: 0.8s all;
}

body.dark .asset-thumbnail img {
  background-color: var(--bg-secondary);
}

.play-button {
  position: absolute;
  bottom: 6px;
  left: 2px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.play-button:hover {
  background-color: rgb(47, 170, 2);
}

/* 删除按钮样式 */
.delete-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.delete-button:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

.asset-item:hover .delete-button {
  opacity: 1;
}

.asset-item:hover .play-button {
  opacity: 1;
}

body.dark .delete-button {
  background-color: rgba(255, 255, 255, 0.2);
}

body.dark .delete-button:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 24px;
}

body.dark .video-placeholder {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.video-duration {
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 10px;
}

/* 音频缩略图样式 */
.audio-thumbnail {
  aspect-ratio: 4/3;
  background-color: #f0f2f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  margin: 0 0 4px 0;
}

.audio-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  height: 60px;
  gap: 6px;
}

.audio-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ecf5ff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.audio-icon .el-icon {
  font-size: 24px;
  color: #409eff;
}

.audio-waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  height: 20px;
  width: 80%;
}

.wave-bar {
  width: 3px;
  background-color: #409eff;
  border-radius: 3px;
  height: 100%;
  animation: wave 1.5s ease-in-out infinite;
  opacity: 0.7;
}

.wave-bar:nth-child(1) {
  height: 30%;
  animation-delay: -0.5s;
}

.wave-bar:nth-child(2) {
  height: 60%;
  animation-delay: -0.4s;
}

.wave-bar:nth-child(3) {
  height: 80%;
  animation-delay: -0.3s;
}

.wave-bar:nth-child(4) {
  height: 40%;
  animation-delay: -0.2s;
}

.wave-bar:nth-child(5) {
  height: 70%;
  animation-delay: -0.1s;
}

.wave-bar:nth-child(6) {
  height: 50%;
  animation-delay: 0s;
}

.wave-bar:nth-child(7) {
  height: 90%;
  animation-delay: 0.1s;
}

.wave-bar:nth-child(8) {
  height: 45%;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(9) {
  height: 65%;
  animation-delay: 0.3s;
}

.audio-duration {
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 10px;
}

@keyframes wave {

  0%,
  100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(0.8);
  }
}

/* 深色模式下的音频缩略图样式 */
body.dark .audio-thumbnail {
  background-color: var(--bg-secondary);
}

body.dark .audio-icon {
  background-color: rgba(64, 158, 255, 0.1);
}

body.dark .audio-icon .el-icon {
  color: var(--primary-color);
}

body.dark .wave-bar {
  background-color: var(--primary-color);
}

.asset-info {
  /* padding: 4px; */
}

.asset-name {
  font-size: 13px;
  font-weight: 500;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

body.dark .asset-name {
  color: var(--text-secondary);
}

.asset-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
  display: none !important;
}

body.dark .asset-meta {
  color: var(--text-secondary);
}

/* 音频项样式 */
.audio-item {
  display: flex;
  align-items: center;
  padding: 10px;
  height: 60px;
}

body.dark .audio-icon {
  background-color: var(--bg-tertiary);
}

.audio-icon .el-icon {
  font-size: 20px;
  color: #409eff;
}

body.dark .audio-icon .el-icon {
  color: var(--primary-color);
}

.audio-item .asset-info {
  flex: 1;
  padding: 0;
}

.audio-actions {
  margin-left: 10px;
}

/* 上传按钮样式 */
.upload-item {
  border-style: dashed;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

body.dark .upload-content {
  color: var(--text-secondary);
}

.upload-content .el-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

/* 加载中状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
  /* 确保显示在内容上方 */
}

.loading-icon {
  font-size: 24px;
  color: #409eff;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-icon {
  color: var(--primary-color);
}

.loading-text {
  font-size: 14px;
  color: #909399;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
  /* 确保显示在内容上方 */
  box-sizing: border-box;
}

.empty-state .el-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

body.dark .empty-state .el-icon {
  color: #606266;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 - 修改选择器为 .assets-list-wrapper */
.assets-list-wrapper::-webkit-scrollbar {
  width: 6px;
}

.assets-list-wrapper::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.assets-list-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

body.dark .assets-list-wrapper::-webkit-scrollbar-thumb {
  background-color: #606266;
}

body.dark .assets-list-wrapper::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

body.dark .loading-state,
body.dark .empty-state {
  background-color: rgba(30, 30, 30, 0.8);
}

/* 添加多文件上传进度样式 */
.file-upload-item {
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 2px;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
  color: #606266;
}

.file-progress {
  color: #409eff;
}

body.dark .file-name {
  color: var(--text-primary);
}

body.dark .file-progress {
  color: var(--primary-color);
}
</style>