<template>
  <div class="volume-control-panel">
    <div class="panel-header">
      <div class="header-title">
        <el-icon><Headset /></el-icon>
        <span>音量设置</span>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 背景音乐音量 -->
      <div class="volume-section">
        <div class="volume-label">
          <el-icon><Headset /></el-icon>
          <span>背景音乐</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="backgroundMusicVolume"
            :min="0"
            :max="100"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            @input="onBackgroundMusicVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value">{{ backgroundMusicVolume }}%</span>
        </div>
      </div>

      <!-- 当前分镜视频音量 -->
      <div class="volume-section" v-if="currentShot && currentShot.type === 'video'">
        <div class="volume-label">
          <el-icon><VideoCamera /></el-icon>
          <span>视频音量</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="videoVolume"
            :min="0"
            :max="100"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            @input="onVideoVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value">{{ videoVolume }}%</span>
        </div>
      </div>

      <!-- 当前分镜音频音量 -->
      <div class="volume-section" v-if="currentShot && currentShot.audios && currentShot.audios.length > 0">
        <div class="volume-label">
          <el-icon><Microphone /></el-icon>
          <span>音频音量</span>
        </div>
        <div class="volume-control">
          <el-slider
            v-model="overallAudioVolume"
            :min="0"
            :max="100"
            :step="1"
            :show-tooltip="true"
            :format-tooltip="formatTooltip"
            @input="onOverallAudioVolumeChange"
            class="volume-slider"
          />
          <span class="volume-value">{{ overallAudioVolume }}%</span>
        </div>
      </div>

      <!-- 当没有选中分镜时的提示 -->
      <div class="no-shot-tip" v-if="!currentShot">
        <el-icon><InfoFilled /></el-icon>
        <span>请选择一个分镜来调整音量</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { 
  Headset, 
  VideoCamera, 
  Microphone, 
  InfoFilled 
} from '@element-plus/icons-vue';

// 组件属性
const props = defineProps({
  currentShot: {
    type: Object,
    default: () => null
  },
  backgroundMusic: {
    type: Object,
    default: () => null
  }
});

// 事件
const emit = defineEmits(['update:backgroundMusicVolume', 'update:videoVolume', 'update:audioVolume']);

// 背景音乐音量 (0-100)
const backgroundMusicVolume = ref(100);

// 视频音量 (0-100)
const videoVolume = ref(100);

// 整体音频音量 (0-100)
const overallAudioVolume = ref(100);

// 格式化提示文本
const formatTooltip = (value) => {
  return `${value}%`;
};

// 背景音乐音量变化
const onBackgroundMusicVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:backgroundMusicVolume', normalizedValue);
};

// 视频音量变化
const onVideoVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:videoVolume', normalizedValue);
};

// 整体音频音量变化
const onOverallAudioVolumeChange = (value) => {
  const normalizedValue = value / 100; // 转换为 0.00-1.00
  emit('update:audioVolume', normalizedValue);
};

// 监听背景音乐变化
watch(() => props.backgroundMusic, (newBgMusic) => {
  if (newBgMusic && typeof newBgMusic.volume === 'number') {
    backgroundMusicVolume.value = Math.round(newBgMusic.volume * 100);
  } else {
    backgroundMusicVolume.value = 100;
  }
}, { immediate: true, deep: true });

// 监听当前分镜变化
watch(() => props.currentShot, (newShot) => {
  if (newShot) {
    // 更新视频音量
    if (newShot.type === 'video' && typeof newShot.videoVolume === 'number') {
      videoVolume.value = Math.round(newShot.videoVolume * 100);
    } else {
      videoVolume.value = 100;
    }

    // 更新整体音频音量（取第一个音频的音量作为整体音量，如果没有音频则为100%）
    if (newShot.audios && newShot.audios.length > 0) {
      const firstAudio = newShot.audios[0];
      if (typeof firstAudio.volume === 'number') {
        overallAudioVolume.value = Math.round(firstAudio.volume * 100);
      } else {
        overallAudioVolume.value = 100;
      }
    } else {
      overallAudioVolume.value = 100;
    }
  } else {
    videoVolume.value = 100;
    overallAudioVolume.value = 100;
  }
}, { immediate: true, deep: true });

// 组件挂载时初始化
onMounted(() => {
  // 初始化背景音乐音量
  if (props.backgroundMusic && typeof props.backgroundMusic.volume === 'number') {
    backgroundMusicVolume.value = Math.round(props.backgroundMusic.volume * 100);
  }

  // 初始化当前分镜音量
  if (props.currentShot) {
    if (props.currentShot.type === 'video' && typeof props.currentShot.videoVolume === 'number') {
      videoVolume.value = Math.round(props.currentShot.videoVolume * 100);
    }

    // 初始化整体音频音量
    if (props.currentShot.audios && props.currentShot.audios.length > 0) {
      const firstAudio = props.currentShot.audios[0];
      if (typeof firstAudio.volume === 'number') {
        overallAudioVolume.value = Math.round(firstAudio.volume * 100);
      }
    }
  }
});
</script>

<style scoped>
.volume-control-panel {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  margin: 0 8px 8px 8px;
  overflow: hidden;
}

body.dark .volume-control-panel {
  background: var(--bg-secondary-video);
  border-color: var(--border-color-dark);
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

body.dark .panel-header {
  background: var(--bg-tertiary-video);
  border-bottom-color: var(--border-color-dark);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

body.dark .header-title {
  color: var(--text-primary-dark);
}

.panel-content {
  padding: 12px;
}

.volume-section {
  margin-bottom: 4px;
}

.volume-section:last-child {
  margin-bottom: 0;
}

.volume-label {
  display: flex;
  align-items: center;
  gap: 6px;
  /* margin-bottom: 8px; */
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

body.dark .volume-label {
  color: var(--text-secondary-dark);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-slider {
  flex: 1;
}

.volume-value {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
  text-align: right;
}

body.dark .volume-value {
  color: var(--text-tertiary-dark);
}



.no-shot-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
  font-size: 13px;
  text-align: center;
}

body.dark .no-shot-tip {
  color: var(--text-tertiary-dark);
}

/* Element Plus 滑块样式覆盖 */
:deep(.el-slider__runway) {
  background-color: #e4e7ed;
}

body.dark :deep(.el-slider__runway) {
  background-color: var(--border-color-dark);
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border-color: #409eff;
}
</style>
