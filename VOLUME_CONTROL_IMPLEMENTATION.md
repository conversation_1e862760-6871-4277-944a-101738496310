# 音量设置组件实现说明

## 功能概述

在 VideoEditor 的 left-panel 最下方新增了一个音量设置组件，支持以下功能：

1. **背景音乐音量设置** - 可设置 `backgroundMusic.volume`（0.00-1.00）
2. **当前分镜视频音量设置** - 可设置 `shots[index].videoVolume`（0.00-1.00）
3. **当前分镜音频音量设置** - 可设置 `shots[index].audios[all].volume`（0.00-1.00）

## 实现文件

### 1. 新增组件文件
- `src/components/videoEdit/VolumeControlPanel.vue` - 音量控制面板组件

### 2. 修改的文件
- `src/views/VideoEditor.vue` - 主编辑器页面，集成音量控制组件

### 3. 测试文件
- `src/components/test/VolumeControlTest.vue` - 音量控制组件测试页面

## 组件特性

### VolumeControlPanel 组件

#### Props
- `currentShot` - 当前选中的分镜对象
- `backgroundMusic` - 背景音乐对象

#### Events
- `update:backgroundMusicVolume` - 背景音乐音量变化事件
- `update:videoVolume` - 视频音量变化事件  
- `update:audioVolume` - 音频音量变化事件

#### 界面功能
1. **背景音乐音量控制**
   - 显示音乐图标和"背景音乐"标签
   - 滑块控制音量（0-100%）
   - 实时显示当前音量百分比

2. **视频音量控制**（仅当选中视频分镜时显示）
   - 显示视频图标和"视频音量"标签
   - 滑块控制音量（0-100%）
   - 实时显示当前音量百分比

3. **音频音量控制**（仅当分镜包含音频时显示）
   - 为每个音频单独显示控制项
   - 根据音频类型显示不同名称（语音/音效）
   - 每个音频都有独立的音量滑块

4. **智能提示**
   - 当没有选中分镜时显示提示信息

## 数据结构

### 背景音乐对象
```javascript
{
  id: 'bg-music-1',
  name: '背景音乐名称',
  audioUrl: 'https://example.com/music.mp3',
  volume: 0.8, // 0.0-1.0 范围
  audioDuration: 120000
}
```

### 分镜对象
```javascript
{
  id: 'shot-1',
  type: 'video', // 'image' 或 'video'
  videoUrl: 'https://example.com/video.mp4',
  videoVolume: 0.6, // 0.0-1.0 范围，视频音量
  audios: [
    {
      id: 'audio-1',
      text: '音频文本内容',
      audioType: 1, // 1-语音, 2-音效
      volume: 0.7, // 0.0-1.0 范围
      audioDuration: 5000
    }
  ]
}
```

## 事件处理

### VideoEditor.vue 中的事件处理方法

1. **handleBackgroundMusicVolumeChange(volume)**
   - 处理背景音乐音量变化
   - 调用 `handleSetBackgroundMusic` API 更新服务器
   - 更新本地 `bgAudioTrack` 状态

2. **handleVideoVolumeChange(volume)**
   - 处理视频音量变化
   - 调用 `updateShot` 方法更新分镜数据

3. **handleAudioVolumeChange({ index, volume })**
   - 处理音频音量变化
   - 更新指定索引的音频音量
   - 调用 `updateShot` 方法更新分镜数据

## 样式特性

- 支持明暗主题切换
- 响应式设计
- 与现有 VideoEditor 界面风格一致
- 使用 Element Plus 组件库的滑块组件

## 兼容性处理

在 `fetchCanvasDetail` 方法中添加了默认值处理：
- `videoVolume` 默认值为 1.0
- `audios[].volume` 默认值为 1.0

确保从服务器获取的数据始终包含音量属性。

## 使用方式

1. 在 VideoEditor 页面中，当有分镜数据时，音量控制面板会自动显示在左侧面板底部
2. 用户可以通过滑块调整各种音量设置
3. 音量变化会实时反映到数据中，并通过 API 同步到服务器

## 测试

可以通过访问 `VolumeControlTest.vue` 组件来测试音量控制功能：
- 模拟不同类型的分镜数据
- 测试各种音量变化事件
- 查看事件日志和数据变化
